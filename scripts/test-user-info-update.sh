#!/bin/bash

# 测试用户信息更新接口
echo "=== 测试用户信息更新接口 ==="

# 服务器地址
SERVER_URL="http://localhost:8082"

# 测试用户ID（从登录返回的数据中获取）
USER_ID="1"

# 测试JWT Token（从登录返回的数据中获取）
TOKEN="eyJhbGciOiJIUzUxMiJ9.eyJvcGVuaWQiOiJvbjNVOTVCbnRuNTFsV19RUTRQWnZtMnkxR0hjIiwibmlja25hbWUiOiLlvq7kv6HnlKjmiLciLCJ1c2VySWQiOjEsInN1YiI6IjEiLCJpc3MiOiJjb29rLWFwcCIsImlhdCI6MTc1MjM4NDY2OCwiZXhwIjoxNzUyOTg5NDY4fQ.6RrNT1yiS5zc_qygrLLBhHIG1PwlqdiQ3qmdJe4oAWaf2H7eeSD7xlPA2qJMHsO49xfaBYuunT2lLfxYlohCiw"

echo "1. 测试更新用户信息接口..."
echo "请求URL: $SERVER_URL/user/update-profile/$USER_ID"

# 发送更新用户信息请求
curl -X POST "$SERVER_URL/user/update-profile/$USER_ID" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "nickName": "测试用户昵称",
    "avatarUrl": "https://example.com/avatar.jpg",
    "gender": 1,
    "country": "中国",
    "province": "广东省",
    "city": "深圳市",
    "language": "zh_CN"
  }' \
  -w "\n\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s

echo ""
echo "2. 测试获取用户信息接口..."
echo "请求URL: $SERVER_URL/user/info/$USER_ID"

# 获取更新后的用户信息
curl -X GET "$SERVER_URL/user/info/$USER_ID" \
  -H "Authorization: Bearer $TOKEN" \
  -w "\n\n状态码: %{http_code}\n响应时间: %{time_total}s\n" \
  -s

echo ""
echo "=== 测试完成 ==="
echo ""
echo "使用说明："
echo "1. 请将 USER_ID 和 TOKEN 替换为实际的值"
echo "2. 确保应用程序正在运行在 localhost:8082"
echo "3. 检查返回的用户信息是否包含更新的数据"
