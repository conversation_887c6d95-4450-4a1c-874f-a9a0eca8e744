package com.cook.controller;

import com.cook.model.base.ResultData;
import com.cook.model.dto.WxLoginDto;
import com.cook.model.vo.LoginResultVo;
import com.cook.model.vo.UserInfoVo;
import com.cook.service.UserInfoService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description 用户信息相关控制器
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 11:36
 */
@RestController
@RequestMapping("/user")
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class UserInfoController {

    final UserInfoService userInfoService;

    /**
     * 微信小程序登录
     * @param dto 登录参数
     * @return 登录结果
     */
    @PostMapping(value = "/wx-login", consumes = "application/json", produces = "application/json")
    public ResultData<LoginResultVo> wxLogin(@Valid @RequestBody WxLoginDto dto) {
        return userInfoService.wxLogin(dto);
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/info/{userId}")
    public ResultData<UserInfoVo> getUserInfo(@PathVariable Long userId) {
        return userInfoService.getUserInfo(userId);
    }

    /**
     * 刷新用户token
     * @param userId 用户ID
     * @return 新的登录结果
     */
    @PostMapping("/refresh-token/{userId}")
    public ResultData<LoginResultVo> refreshToken(@PathVariable Long userId) {
        return userInfoService.refreshToken(userId);
    }

    /**
     * 微信小程序更新用户信息
     * @param dto 用户信息
     * @return 更新结果
     */
    @PostMapping(value = "/wx-update", consumes = "application/json", produces = "application/json")
    public ResultData<String> wxUpdate(@RequestBody UserInfoVo dto) {
        return userInfoService.updateUserInfo(dto);
    }
}
