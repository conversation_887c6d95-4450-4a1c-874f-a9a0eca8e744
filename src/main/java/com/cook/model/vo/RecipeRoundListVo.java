package com.cook.model.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 菜谱列表响应VO
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecipeRoundListVo {
    
    /**
     * 菜谱ID
     */
    private Integer id;
    
    /**
     * 菜名
     */
    private String name;

    
    /**
     * 难度等级（1-3）
     */
    private Integer difficultyLevel;

}
