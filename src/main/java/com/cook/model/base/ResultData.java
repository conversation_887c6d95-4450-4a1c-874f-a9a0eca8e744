package com.cook.model.base;
import com.cook.common.enums.ApiCodeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 12:00
 */
//@ApiModel
@Data
public class ResultData<T> {
    @JsonProperty
    private int code;
    @JsonProperty
    private String msg;
    @JsonProperty
    private T data;

    private final static String SUCCESS = "success";
    private final static String ERROR = "error";

    public ResultData() {
    }

    public ResultData(ApiCodeEnum apiCodeEnum, String msg, T data) {
        this.code = apiCodeEnum.getCode();
        this.msg = msg == null ? ApiCodeEnum.getMessage(apiCodeEnum) : msg;
        this.data = data;
    }

    public ResultData(ApiCodeEnum apiCodeEnum, String msg) {
        this.code = apiCodeEnum.getCode();
        this.msg = msg == null ? ApiCodeEnum.getMessage(apiCodeEnum) : msg;
    }

    public ResultData(ApiCodeEnum apiCodeEnum) {
        this.code = apiCodeEnum.getCode();
        this.msg = msg == null ? ApiCodeEnum.getMessage(apiCodeEnum) : msg;
    }

    public static ResultData success() {
        return new ResultData(ApiCodeEnum.SUCCESS);
    }

    public static ResultData success(String msg) {
        return new ResultData(ApiCodeEnum.SUCCESS, msg);
    }

    public static ResultData success(String msg, Object data) {
        return new ResultData(ApiCodeEnum.SUCCESS, msg, data);
    }

    public static ResultData success(Object data) {
        return new ResultData(ApiCodeEnum.SUCCESS, null, data);
    }

    public static ResultData fail() {
        return new ResultData(ApiCodeEnum.ERROR);
    }

    public static ResultData fail(String msg) {
        return new ResultData(ApiCodeEnum.ERROR, msg);
    }

    public static ResultData fail(ApiCodeEnum apiCodeEnum, String msg) {
        return new ResultData(apiCodeEnum, msg);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return this.code == ApiCodeEnum.SUCCESS.getCode();
    }
}