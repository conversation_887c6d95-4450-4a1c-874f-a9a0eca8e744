package com.cook.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cook.common.util.ImageBase64Util;
import com.cook.common.util.UserContext;
import com.cook.model.base.ResultData;
import com.cook.model.dto.BaiDuTokenResult;
import com.cook.model.dto.BaiDuVersionsResult;
import com.cook.model.dto.CookImageInfoDto;
import com.cook.model.dto.CookVersionRecordPageDto;
import com.cook.model.entity.CookVersionRecord;
import com.cook.model.mapper.CookVersionRecordRepository;
import com.cook.model.vo.CookAiVersionInfoVo;
import com.cook.model.vo.PageResult;
import com.cook.service.CookInfoService;
import com.cook.service.client.BaiduOssService;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import com.cook.service.client.BaiduServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 文件描述
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/14 12:23
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Autowired})
public class CookInfoServiceImpl implements CookInfoService {
    final BaiduServiceClient baiduServiceClient;
    final BaiduIngredientService baiduIngredientService;
    final CookVersionRecordRepository cookVersionRecordRepository;
    final BaiduOssService baiduOssService;
    @Value("${baidu.grant.type:client_credentials}")
    private String baiduGrantType;
    @Value("${baidu.api.key:}")
    private String baiduApiKey;
    @Value("${baidu.api.secret:}")
    private String baiduApiSecret;
    @Value("${baidu.ingredient.top-num:10}")
    private Integer baiduTopNum;
    @Value("${baidu.ingredient.min-score:0.5}")
    private Double baiduMinScore;
    private static final NumberFormat PERCENT_FORMAT = NumberFormat.getPercentInstance(Locale.CHINA);

    @Override
    public ResultData<List<CookAiVersionInfoVo>> upload(CookImageInfoDto dto) {
        log.info("参数：{}", JSONUtil.toJsonStr(dto));

        // 获取当前用户ID
        Long currentUserId = UserContext.getCurrentUserId();
        if (currentUserId == null) {
            currentUserId = 0L; // 临时设置为0，实际应该要求用户登录
        }

        // 获取百度访问令牌
        BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
        log.info("百度token：{}", JSONUtil.toJsonStr(token));

        if (token == null || StrUtil.isBlank(token.getAccessToken())) {
            return ResultData.fail("获取百度访问令牌失败");
        }

        List<CookAiVersionInfoVo> list = CollUtil.toList();
        PERCENT_FORMAT.setMaximumFractionDigits(2);

        // 遍历处理每个图片文件
        for (CookImageInfoDto.Info file : dto.getFiles()) {
            String fileBase64 = file.getFileData();
            InputStream inputStream = ImageBase64Util.base64ToInputStream(fileBase64);
            long fileId = baiduOssService.uploadFile(inputStream, "png");
            file.setFileId(fileId);
            file.setFileData(null);

            try {
                // 使用百度食材识别服务（支持Feign和HTTP两种方式）
                BaiDuVersionsResult versionsResult = baiduIngredientService.classifyIngredient(
                        token.getAccessToken(),
                        fileBase64
                );

                log.info("文件 {} 识别结果：{}", file.getFileName(), JSONUtil.toJsonStr(versionsResult));

                if (versionsResult != null && !CollUtil.isEmpty(versionsResult.getResult())) {
                    List<CookAiVersionInfoVo> tempList = versionsResult.getResult()
                            .stream()
                            .filter(x -> x.getScore() > baiduMinScore) // 使用配置的最小置信度
                            .map(x -> CookAiVersionInfoVo.builder()
                                    .name(x.getName())
                                    .rate(PERCENT_FORMAT.format(x.getScore()))
                                    .build())
                            .collect(Collectors.toList());

                    if (!tempList.isEmpty()) {
                        list.addAll(tempList);
                        log.info("文件 {} 识别到 {} 个有效食材", file.getFileName(), tempList.size());
                    } else {
                        log.warn("文件 {} 识别结果置信度均低于阈值 {}", file.getFileName(), baiduMinScore);
                    }
                } else {
                    log.warn("文件 {} 未识别到任何食材", file.getFileName());
                }

            } catch (Exception e) {
                log.error("调用百度食材识别接口失败，文件：{}", file.getFileName(), e);
                // 单个文件识别失败不影响其他文件，继续处理
            }
        }
        if (CollUtil.isEmpty(list)) {
            CookVersionRecord cookVersionRecord = CookVersionRecord.builder()
                    .userId(currentUserId)
                    .uploadParams(JSONUtil.toJsonStr(dto))
                    .build();
            cookVersionRecordRepository.save(cookVersionRecord);
            return ResultData.fail("未识别到食材，请拍清晰的照片");
        }

        CookVersionRecord cookVersionRecord = CookVersionRecord.builder()
                .userId(currentUserId)
                .uploadParams(JSONUtil.toJsonStr(dto))
                .resultParams(JSONUtil.toJsonStr(list))
                .build();
        cookVersionRecordRepository.save(cookVersionRecord);
        return ResultData.success(list);
    }

    @Override
    public ResultData<List<CookAiVersionInfoVo>> uploadMultipart(MultipartFile[] files, String description, Boolean enableAiRecognition) {
        try {
            log.info("开始处理多文件上传，文件数量：{}，描述：{}，启用AI识别：{}", files.length, description, enableAiRecognition);

            // 验证文件数组
            if (files == null || files.length == 0) {
                return ResultData.fail("文件列表不能为空");
            }

            // 限制文件数量
            if (files.length > 10) {
                return ResultData.fail("单次最多只能上传10个文件");
            }

            // 获取当前用户ID
            Long currentUserId = UserContext.getCurrentUserId();
            if (currentUserId == null) {
                currentUserId = 0L; // 临时设置为0，实际应该要求用户登录
            }

            // 创建上传记录
            CookVersionRecord cookVersionRecord = CookVersionRecord.builder()
                    .userId(currentUserId)
                    .uploadParams(JSONUtil.toJsonStr(Map.of(
                            "fileCount", files.length,
                            "description", description != null ? description : "",
                            "enableAiRecognition", enableAiRecognition
                    )))
                    .build();
            cookVersionRecordRepository.save(cookVersionRecord);

            // 如果不启用AI识别，直接返回成功
            if (!enableAiRecognition) {
                log.info("AI识别已禁用，直接返回成功");
                return ResultData.success(CollUtil.toList());
            }

            // 获取百度访问令牌
            BaiDuTokenResult token = baiduServiceClient.getAccessToken(baiduGrantType, baiduApiKey, baiduApiSecret);
            log.info("百度token：{}", JSONUtil.toJsonStr(token));

            if (token == null || StrUtil.isBlank(token.getAccessToken())) {
                return ResultData.fail("获取百度访问令牌失败");
            }

            List<CookAiVersionInfoVo> allResults = CollUtil.toList();
            PERCENT_FORMAT.setMaximumFractionDigits(2);

            // 遍历处理每个文件
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                try {
                    log.info("正在处理第{}个文件：{}", i + 1, file.getOriginalFilename());

                    // 验证文件类型
                    if (!isValidImageFile(file)) {
                        log.warn("文件{}不是有效的图片格式，跳过", file.getOriginalFilename());
                        continue;
                    }

                    // 将文件转换为Base64
                    String fileBase64 = Base64.encode(file.getBytes());

                    // 使用百度食材识别服务
                    BaiDuVersionsResult versionsResult = baiduIngredientService.classifyIngredient(
                            token.getAccessToken(),
                            fileBase64
                    );

                    log.info("文件 {} 识别结果：{}", file.getOriginalFilename(), JSONUtil.toJsonStr(versionsResult));

                    // 处理识别结果
                    if (versionsResult != null && !CollUtil.isEmpty(versionsResult.getResult())) {
                        List<CookAiVersionInfoVo> tempList = versionsResult.getResult()
                                .stream()
                                .filter(x -> x.getScore() > baiduMinScore) // 使用配置的最小置信度
                                .map(x -> CookAiVersionInfoVo.builder()
                                        .name(x.getName())
                                        .rate(PERCENT_FORMAT.format(x.getScore()))
                                        .build())
                                .collect(Collectors.toList());

                        if (!tempList.isEmpty()) {
                            allResults.addAll(tempList);
                            log.info("文件 {} 识别到 {} 个有效食材", file.getOriginalFilename(), tempList.size());
                        } else {
                            log.warn("文件 {} 识别结果置信度均低于阈值 {}", file.getOriginalFilename(), baiduMinScore);
                        }
                    } else {
                        log.warn("文件 {} 未识别到任何食材", file.getOriginalFilename());
                    }

                } catch (Exception e) {
                    log.error("处理文件{}时发生异常", file.getOriginalFilename(), e);
                    // 单个文件处理失败不影响其他文件，继续处理
                }
            }

            // 更新记录结果
            cookVersionRecord.setResultParams(JSONUtil.toJsonStr(allResults));
            cookVersionRecordRepository.updateById(cookVersionRecord);

            if (CollUtil.isEmpty(allResults)) {
                return ResultData.fail("未识别到食材，请上传清晰的食材照片");
            }

            log.info("多文件上传处理完成，共识别到{}个食材", allResults.size());
            return ResultData.success(allResults);

        } catch (Exception e) {
            log.error("多文件上传处理失败", e);
            return ResultData.fail("处理失败：" + e.getMessage());
        }
    }

    /**
     * 验证是否为有效的图片文件
     */
    private boolean isValidImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        String contentType = file.getContentType();
        if (contentType == null) {
            return false;
        }

        // 支持的图片类型
        return contentType.startsWith("image/") &&
               (contentType.contains("jpeg") || contentType.contains("jpg") ||
                contentType.contains("png") || contentType.contains("gif") ||
                contentType.contains("bmp") || contentType.contains("webp"));
    }

    @Override
    public ResultData<PageResult<CookAiVersionInfoVo>> getVersionRecordsPage(CookVersionRecordPageDto dto) {
        log.info("分页查询参数：{}", JSONUtil.toJsonStr(dto));

        // 构建查询条件，只查询必要字段，排除upload_params
        LambdaQueryWrapper<CookVersionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CookVersionRecord::getId,
                           CookVersionRecord::getUserId,
                           CookVersionRecord::getResultParams,
                           CookVersionRecord::getCreateTime,
                           CookVersionRecord::getUpdateTime)
                .eq(CookVersionRecord::getUserId, dto.getUserId())
                .isNotNull(CookVersionRecord::getResultParams)
                .ne(CookVersionRecord::getResultParams, "")
                .orderByDesc(CookVersionRecord::getId);

        // 执行分页查询
        Page<CookVersionRecord> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<CookVersionRecord> resultPage = cookVersionRecordRepository.page(page, queryWrapper);

        // 转换数据
        List<CookAiVersionInfoVo> voList = CollUtil.toList();
        if (CollUtil.isNotEmpty(resultPage.getRecords())) {
            for (CookVersionRecord record : resultPage.getRecords()) {
                if (StrUtil.isNotBlank(record.getResultParams())) {
                    try {
                        String createTime = DateUtil.format(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss");
                        List<CookAiVersionInfoVo> tempList = JSONUtil.toList(record.getResultParams(), CookAiVersionInfoVo.class);
                        if (CollUtil.isNotEmpty(tempList)) {
                            tempList.forEach(x -> {
                                x.setCreateTime(createTime);
                                x.setId(record.getId());
                            });
                            voList.addAll(tempList);
                        }
                    } catch (Exception e) {
                        log.warn("解析resultParams失败，recordId: {}, resultParams: {}", record.getId(), record.getResultParams(), e);
                    }
                }
            }
        }

        // 构建分页结果
        PageResult<CookAiVersionInfoVo> pageResult = PageResult.build(
                voList,
                resultPage.getTotal(),
                dto.getPageNum(),
                dto.getPageSize()
        );

        return ResultData.success(pageResult);
    }
}
