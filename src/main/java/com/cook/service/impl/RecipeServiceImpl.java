package com.cook.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cook.model.base.ResultData;
import com.cook.model.dto.RecipeSearchDto;
import com.cook.model.entity.CookRecipe;
import com.cook.model.entity.CookRecipeIngredient;
import com.cook.model.entity.CookRecipeStep;
import com.cook.model.mapper.CookRecipeIngredientRepository;
import com.cook.model.mapper.CookRecipeRepository;
import com.cook.model.mapper.CookRecipeStepRepository;
import com.cook.model.vo.PageResult;
import com.cook.model.vo.RecipeDetailVo;
import com.cook.model.vo.RecipeListVo;
import com.cook.model.vo.RecipeRoundListVo;
import com.cook.service.RecipeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 菜谱服务实现类
 * @copyright <铜豌豆-1forall.cn>
 * @since 2025/6/25
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = { @Autowired })
public class RecipeServiceImpl implements RecipeService {

    final CookRecipeRepository cookRecipeRepository;
    final CookRecipeIngredientRepository cookRecipeIngredientRepository;
    final CookRecipeStepRepository cookRecipeStepRepository;

    @Override
    public ResultData<PageResult<RecipeListVo>> searchRecipes(RecipeSearchDto dto) {
        log.info("搜索菜谱参数：{}", JSONUtil.toJsonStr(dto));

        try {
            List<CookRecipe> recipes;

            // 如果有食材名称，优先根据食材搜索
            if (CollUtil.isNotEmpty(dto.getIngredientNames())) {
                recipes = cookRecipeRepository.findRecipesByIngredientNames(dto.getIngredientNames());
            } else {
                // 构建查询条件
                LambdaQueryWrapper<CookRecipe> queryWrapper = new LambdaQueryWrapper<>();

                // 菜名模糊匹配
                if (StrUtil.isNotBlank(dto.getRecipeName())) {
                    queryWrapper.like(CookRecipe::getName, dto.getRecipeName());
                }

                // 难度等级匹配
                if (dto.getDifficultyLevel() != null) {
                    queryWrapper.eq(CookRecipe::getDifficultyLevel, dto.getDifficultyLevel());
                }

                // 执行分页查询
                Page<CookRecipe> page = new Page<>(dto.getPageNum(), dto.getPageSize());
                Page<CookRecipe> resultPage = cookRecipeRepository.page(page, queryWrapper);
                recipes = resultPage.getRecords();
            }

            // 转换为VO
            List<RecipeListVo> voList = recipes.stream()
                    .map(this::convertToRecipeListVo)
                    .collect(Collectors.toList());

            // 构建分页结果
            PageResult<RecipeListVo> pageResult = PageResult.build(
                    voList,
                    (long) recipes.size(),
                    dto.getPageNum(),
                    dto.getPageSize());

            return ResultData.success(pageResult);

        } catch (Exception e) {
            log.error("搜索菜谱异常", e);
            return ResultData.fail("搜索菜谱失败，请稍后重试");
        }
    }

    @Override
    public ResultData<RecipeDetailVo> getRecipeDetail(Integer recipeId) {
        log.info("获取菜谱详情，ID：{}", recipeId);

        try {
            // 1. 获取菜谱基本信息
            CookRecipe recipe = cookRecipeRepository.getById(recipeId);
            if (recipe == null) {
                return ResultData.fail("菜谱不存在");
            }

            // 2. 获取食材信息
            List<CookRecipeIngredient> ingredients = cookRecipeIngredientRepository.findIngredientsByRecipeId(recipeId);
            List<RecipeDetailVo.RecipeIngredientVo> ingredientVos = ingredients.stream()
                    .map(this::convertToRecipeIngredientVo)
                    .collect(Collectors.toList());

            // 3. 获取制作步骤
            LambdaQueryWrapper<CookRecipeStep> stepWrapper = new LambdaQueryWrapper<>();
            stepWrapper.eq(CookRecipeStep::getRecipeId, recipeId)
                    .orderByAsc(CookRecipeStep::getStepOrder);
            List<CookRecipeStep> steps = cookRecipeStepRepository.list(stepWrapper);
            List<RecipeDetailVo.RecipeStepVo> stepVos = steps.stream()
                    .map(this::convertToRecipeStepVo)
                    .collect(Collectors.toList());

            // 4. 构建详情VO
            RecipeDetailVo detailVo = RecipeDetailVo.builder()
                    .id(recipe.getId())
                    .name(recipe.getName())
                    .description(recipe.getDescription())
                    .difficultyLevel(recipe.getDifficultyLevel())
                    .difficultyLevelDesc(getDifficultyLevelDesc(recipe.getDifficultyLevel()))
                    .servings(recipe.getServings())
                    .ingredients(ingredientVos)
                    .steps(stepVos)
                    .build();

            return ResultData.success(detailVo);

        } catch (Exception e) {
            log.error("获取菜谱详情异常", e);
            return ResultData.fail("获取菜谱详情失败，请稍后重试");
        }
    }

    /**
     * 转换为菜谱列表VO
     */
    private RecipeListVo convertToRecipeListVo(CookRecipe recipe) {
        RecipeListVo vo = new RecipeListVo();
        BeanUtils.copyProperties(recipe, vo);
        vo.setDifficultyLevelDesc(getDifficultyLevelDesc(recipe.getDifficultyLevel()));
        return vo;
    }

    /**
     * 转换为食材VO
     */
    private RecipeDetailVo.RecipeIngredientVo convertToRecipeIngredientVo(CookRecipeIngredient ingredient) {
        return RecipeDetailVo.RecipeIngredientVo.builder()
                .ingredientId(ingredient.getIngredientId())
                .ingredientName(getIngredientName(ingredient))
                .quantity(ingredient.getQuantity())
                .type(ingredient.getType())
                .typeDesc(getIngredientTypeDesc(ingredient.getType()))
                .build();
    }

    /**
     * 转换为步骤VO
     */
    private RecipeDetailVo.RecipeStepVo convertToRecipeStepVo(CookRecipeStep step) {
        return RecipeDetailVo.RecipeStepVo.builder()
                .id(step.getId())
                .stepOrder(step.getStepOrder())
                .description(step.getDescription())
                .durationSeconds(step.getDurationSeconds())
                .durationDesc(getDurationDesc(step.getDurationSeconds()))
                .build();
    }

    /**
     * 获取难度等级描述
     */
    private String getDifficultyLevelDesc(Integer level) {
        if (level == null)
            return "未知";
        Map<Integer, String> levelMap = new HashMap<>();
        levelMap.put(1, "简单");
        levelMap.put(2, "中等");
        levelMap.put(3, "困难");
        return levelMap.getOrDefault(level, "未知");
    }

    /**
     * 获取食材类型描述
     */
    private String getIngredientTypeDesc(String type) {
        if (StrUtil.isBlank(type))
            return "未知";
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("main", "主料");
        typeMap.put("supplement", "辅料");
        typeMap.put("seasoning", "调料");
        return typeMap.getOrDefault(type, "未知");
    }

    /**
     * 获取时长描述
     */
    private String getDurationDesc(Integer seconds) {
        if (seconds == null || seconds <= 0)
            return null;
        if (seconds < 60) {
            return seconds + "秒";
        } else {
            int minutes = seconds / 60;
            int remainSeconds = seconds % 60;
            if (remainSeconds == 0) {
                return minutes + "分钟";
            } else {
                return minutes + "分" + remainSeconds + "秒";
            }
        }
    }

    /**
     * 获取食材名称（从查询结果中提取）
     */
    private String getIngredientName(CookRecipeIngredient ingredient) {
        return ingredient.getIngredientName();
    }

    @Override
    public ResultData<PageResult<RecipeRoundListVo>> randomRecipes(RecipeSearchDto dto) {
        try {
            // 1. 先查询总数，确定有效的页码范围
            LambdaQueryWrapper<CookRecipe> countWrapper = new LambdaQueryWrapper<>();
            countWrapper.in(CookRecipe::getCategory, CollUtil.toList(1, 2, 3, 4));
            long totalCount = cookRecipeRepository.count(countWrapper);

            if (totalCount == 0) {
                return ResultData.success(PageResult.build(CollUtil.newArrayList(), 0L, 1, dto.getPageSize()));
            }

            // 2. 计算最大页数
            int maxPage = (int) Math.ceil((double) totalCount / dto.getPageSize());

            // 3. 生成随机页码（在有效范围内）
            SecureRandom random = new SecureRandom();
            int randomPageNum = random.nextInt(1, maxPage + 1);

            log.info("随机菜谱查询 - 总记录数: {}, 每页大小: {}, 最大页数: {}, 随机页码: {}",
                    totalCount, dto.getPageSize(), maxPage, randomPageNum);

            // 4. 执行分页查询
            LambdaQueryWrapper<CookRecipe> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(CookRecipe::getCategory, CollUtil.toList(1, 2, 3, 4))
                    .orderByAsc(CookRecipe::getId);

            Page<CookRecipe> page = new Page<>(randomPageNum, dto.getPageSize());
            Page<CookRecipe> resultPage = cookRecipeRepository.page(page, queryWrapper);

            log.info("分页查询结果: 当前页={}, 每页大小={}, 总记录数={}, 实际返回记录数={}",
                    resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal(),
                    resultPage.getRecords().size());

            List<CookRecipe> recipes = resultPage.getRecords();

            // 5. 转换为VO
            List<RecipeListVo> voList = recipes.stream()
                    .map(this::convertToRecipeListVo)
                    .collect(Collectors.toList());

            List<RecipeRoundListVo> list = voList.stream().map(x -> RecipeRoundListVo.builder()
                    .id(x.getId())
                    .name(x.getName())
                    .difficultyLevel(x.getDifficultyLevel())
                    .build()).toList();

            // 6. 构建分页结果（使用实际的分页信息）
            PageResult<RecipeRoundListVo> pageResult = PageResult.build(
                    list,
                    resultPage.getTotal(), // 使用实际总数
                    (int) resultPage.getCurrent(), // 使用实际当前页
                    (int) resultPage.getSize() // 使用实际页大小
            );

            return ResultData.success(pageResult);

        } catch (Exception e) {
            log.error("获取随机菜谱异常", e);
            return ResultData.fail("获取随机菜谱失败，请稍后重试");
        }
    }
}
