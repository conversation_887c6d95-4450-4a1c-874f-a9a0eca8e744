# 微信小程序用户信息获取完整方案

## 问题分析

您遇到的问题是微信小程序开发中的常见误区：

### 当前状态
- ✅ 后端成功获取了 `openid` 和 `sessionKey`
- ✅ JWT token 生成正常
- ❌ 用户信息（昵称、头像等）为空

### 根本原因
微信小程序的用户信息获取机制分为两个步骤：
1. **wx.login()** - 只能获取 `openid` 和 `session_key`（后端调用微信API）
2. **wx.getUserProfile()** - 获取用户昵称、头像等信息（前端调用，需要用户授权）

**后端无法直接获取用户的昵称、头像等信息**，这些信息必须由前端获取后传递给后端。

## 完整解决方案

### 1. 前端代码实现

```javascript
// pages/login/login.js
Page({
  data: {
    userInfo: null,
    hasUserInfo: false
  },

  onLoad() {
    // 页面加载时自动登录
    this.wxLogin();
  },

  // 微信登录流程
  wxLogin() {
    wx.showLoading({ title: '登录中...' });
    
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 调用后端登录接口
          this.callBackendLogin(loginRes.code);
        } else {
          wx.hideLoading();
          wx.showToast({ title: '登录失败', icon: 'error' });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({ title: '登录失败', icon: 'error' });
      }
    });
  },

  // 调用后端登录接口
  callBackendLogin(code) {
    wx.request({
      url: 'http://your-backend-url/user/wx-login',
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: { code: code },
      success: (res) => {
        wx.hideLoading();
        
        if (res.data.code === 200) {
          // 保存登录信息
          const loginData = res.data.data;
          wx.setStorageSync('token', loginData.token);
          wx.setStorageSync('userId', loginData.userId);
          wx.setStorageSync('openid', loginData.openid);
          
          wx.showToast({ title: '登录成功', icon: 'success' });
          
          // 登录成功后，引导用户授权获取用户信息
          this.showUserInfoAuth();
        } else {
          wx.showToast({ title: res.data.msg || '登录失败', icon: 'error' });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({ title: '网络错误', icon: 'error' });
      }
    });
  },

  // 显示用户信息授权
  showUserInfoAuth() {
    wx.showModal({
      title: '用户信息授权',
      content: '为了提供更好的服务，需要获取您的用户信息',
      confirmText: '授权',
      cancelText: '跳过',
      success: (res) => {
        if (res.confirm) {
          this.getUserProfile();
        } else {
          // 用户选择跳过，直接进入主页
          this.navigateToMain();
        }
      }
    });
  },

  // 获取用户信息
  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userInfo = res.userInfo;
        this.setData({
          userInfo: userInfo,
          hasUserInfo: true
        });
        
        // 将用户信息发送给后端
        this.updateUserInfoToBackend(userInfo);
      },
      fail: () => {
        wx.showToast({ title: '获取用户信息失败', icon: 'error' });
        // 即使获取用户信息失败，也可以进入主页
        this.navigateToMain();
      }
    });
  },

  // 将用户信息更新到后端
  updateUserInfoToBackend(userInfo) {
    const userId = wx.getStorageSync('userId');
    const token = wx.getStorageSync('token');
    
    wx.request({
      url: `http://your-backend-url/user/update-profile/${userId}`,
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      data: {
        nickName: userInfo.nickName,
        avatarUrl: userInfo.avatarUrl,
        gender: userInfo.gender,
        country: userInfo.country,
        province: userInfo.province,
        city: userInfo.city,
        language: userInfo.language
      },
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({ title: '用户信息更新成功', icon: 'success' });
        }
        this.navigateToMain();
      },
      fail: () => {
        // 即使更新失败，也进入主页
        this.navigateToMain();
      }
    });
  },

  // 进入主页
  navigateToMain() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 手动获取用户信息按钮（可选）
  onGetUserInfo() {
    this.getUserProfile();
  }
});
```

### 2. 前端页面结构

```xml
<!-- pages/login/login.wxml -->
<view class="login-container">
  <view class="logo">
    <image src="/images/logo.png" mode="aspectFit"></image>
  </view>
  
  <view class="title">欢迎使用智能菜谱</view>
  
  <view class="user-info" wx:if="{{hasUserInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>
  
  <button class="login-btn" bindtap="wxLogin" wx:if="{{!hasUserInfo}}">
    微信登录
  </button>
  
  <button class="auth-btn" bindtap="onGetUserInfo" wx:if="{{!hasUserInfo}}">
    获取用户信息
  </button>
</view>
```

### 3. 后端接口说明

#### 3.1 登录接口（已有）
```
POST /user/wx-login
```
返回数据包含：
- `userId`: 用户ID
- `token`: JWT令牌
- `openid`: 微信openid
- `sessionKey`: 微信会话密钥

#### 3.2 更新用户信息接口（新增）
```
POST /user/update-profile/{userId}
```
请求参数：
```json
{
  "nickName": "用户昵称",
  "avatarUrl": "头像URL",
  "gender": 1,
  "country": "中国",
  "province": "广东省",
  "city": "深圳市",
  "language": "zh_CN"
}
```

## 最佳实践建议

### 1. 用户体验优化
- 登录成功后再引导用户授权信息
- 即使用户拒绝授权也能正常使用应用
- 提供后续补充用户信息的入口

### 2. 错误处理
- 网络请求失败的降级处理
- 用户拒绝授权的友好提示
- 登录状态的持久化存储

### 3. 安全考虑
- 使用JWT token进行接口认证
- 敏感信息不要存储在本地
- 定期刷新token

## 测试验证

### 1. 测试登录流程
```javascript
// 测试代码
console.log('Token:', wx.getStorageSync('token'));
console.log('UserId:', wx.getStorageSync('userId'));
console.log('OpenId:', wx.getStorageSync('openid'));
```

### 2. 测试用户信息更新
检查后端数据库中用户信息是否正确更新。

## 总结

正确的流程应该是：
1. **前端调用 wx.login()** → 获取 code
2. **后端调用微信API** → 获取 openid 和 sessionKey
3. **前端调用 wx.getUserProfile()** → 获取用户信息
4. **前端将用户信息发送给后端** → 更新用户资料

这样就能完整地获取到用户的所有信息了。
