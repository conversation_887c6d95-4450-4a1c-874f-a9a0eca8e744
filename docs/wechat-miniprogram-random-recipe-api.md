# 微信小程序随机菜谱接口对接文档

> **重要更新说明**：该接口返回数据结构已优化，现在返回简化版菜谱信息（RecipeRoundListVo），只包含 id、name、difficultyLevel 三个核心字段，以提升接口性能。如需完整菜谱信息，请使用菜谱详情接口。

## 接口概述

### 接口名称
随机获取菜谱列表

### 接口地址
```
GET /recipe/search-by-random
```

### 功能说明
随机获取5条菜谱信息，用于微信小程序首页推荐、随机发现等场景。

### 接口特点
- 无需传递任何参数
- 每次调用返回不同的随机菜谱
- 固定返回5条菜谱记录
- 支持分类筛选（包含1-4类菜谱）

---

## 请求参数

### HTTP方法
```
GET
```

### 请求头
```
Content-Type: application/json
Authorization: Bearer {jwt_token}
```

### 请求参数
无需传递任何参数

---

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "白灼菜心",
        "difficultyLevel": 1
      },
      {
        "id": 15,
        "name": "蒜蓉炒菠菜",
        "difficultyLevel": 2
      },
      {
        "id": 23,
        "name": "红烧肉",
        "difficultyLevel": 3
      },
      {
        "id": 8,
        "name": "西红柿鸡蛋",
        "difficultyLevel": 1
      },
      {
        "id": 42,
        "name": "麻婆豆腐",
        "difficultyLevel": 2
      }
    ],
    "total": 5,
    "pageNum": 0,
    "pageSize": 5,
    "pages": 1,
    "hasNext": false,
    "hasPrevious": false
  }
}
```

### 失败响应
```json
{
  "code": 500,
  "msg": "获取随机菜谱失败，请稍后重试",
  "data": null
}
```

---

## 响应字段说明

### 菜谱列表字段（RecipeRoundListVo）

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | Integer | 菜谱唯一标识 | 1 |
| name | String | 菜谱名称 | "白灼菜心" |
| difficultyLevel | Integer | 难度等级（1-3） | 1 |

**注意：** 随机菜谱接口返回的是简化版本的菜谱信息，只包含基本的 id、name 和 difficultyLevel 字段。如需获取完整的菜谱信息（包括简介、难度描述、份数等），请使用菜谱详情接口 `/recipe/detail/{id}`。

### 分页信息字段（PageResult）

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| list | Array | 菜谱列表数据 | [] |
| total | Long | 总记录数（固定为5） | 5 |
| pageNum | Integer | 当前页码（固定为0） | 0 |
| pageSize | Integer | 每页大小（固定为5） | 5 |
| pages | Integer | 总页数（固定为1） | 1 |
| hasNext | Boolean | 是否有下一页（固定为false） | false |
| hasPrevious | Boolean | 是否有上一页（固定为false） | false |

### 难度等级说明

| 等级值 | 描述 |
|--------|------|
| 1 | 简单 |
| 2 | 中等 |
| 3 | 困难 |

---

## 微信小程序集成示例

### 1. 基础调用示例

```javascript
// 获取随机菜谱
function getRandomRecipes() {
  const token = wx.getStorageSync('jwt_token');
  
  wx.request({
    url: 'https://your-domain.com/recipe/search-by-random',
    method: 'GET',
    header: {
      'Authorization': 'Bearer ' + token
    },
    success: function(res) {
      if (res.data.code === 200) {
        const recipes = res.data.data.list;
        console.log('随机菜谱列表：', recipes);
        // 处理菜谱数据
        handleRandomRecipes(recipes);
      } else {
        wx.showToast({
          title: res.data.msg || '获取失败',
          icon: 'none'
        });
      }
    },
    fail: function(error) {
      console.error('请求失败：', error);
      wx.showToast({
        title: '网络请求失败',
        icon: 'none'
      });
    }
  });
}

// 处理随机菜谱数据
function handleRandomRecipes(recipes) {
  // 更新页面数据
  this.setData({
    randomRecipes: recipes
  });
}
```

### 2. 封装为Promise的调用方式

```javascript
// 封装为Promise
function getRandomRecipesPromise() {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('jwt_token');
    
    wx.request({
      url: 'https://your-domain.com/recipe/search-by-random',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: function(res) {
        if (res.data.code === 200) {
          resolve(res.data.data.list);
        } else {
          reject(new Error(res.data.msg || '获取失败'));
        }
      },
      fail: function(error) {
        reject(error);
      }
    });
  });
}

// 使用示例
async function loadRandomRecipes() {
  try {
    wx.showLoading({ title: '加载中...' });
    const recipes = await getRandomRecipesPromise();
    
    this.setData({
      randomRecipes: recipes
    });
    
    wx.hideLoading();
  } catch (error) {
    wx.hideLoading();
    wx.showToast({
      title: error.message || '获取随机菜谱失败',
      icon: 'none'
    });
  }
}
```

### 3. 页面集成示例

```javascript
// pages/index/index.js
Page({
  data: {
    randomRecipes: []
  },

  onLoad: function() {
    this.loadRandomRecipes();
  },

  // 下拉刷新获取新的随机菜谱
  onPullDownRefresh: function() {
    this.loadRandomRecipes().then(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 加载随机菜谱
  loadRandomRecipes: function() {
    return getRandomRecipesPromise()
      .then(recipes => {
        this.setData({
          randomRecipes: recipes
        });
      })
      .catch(error => {
        wx.showToast({
          title: '获取随机菜谱失败',
          icon: 'none'
        });
      });
  },

  // 点击菜谱查看详情
  onRecipeClick: function(e) {
    const recipeId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/recipe-detail/index?id=${recipeId}`
    });
  },

  // 刷新随机菜谱
  refreshRandomRecipes: function() {
    this.loadRandomRecipes();
  }
});
```

### 4. WXML模板示例

```xml
<!-- pages/index/index.wxml -->
<view class="container">
  <view class="section-title">今日推荐</view>

  <view class="recipe-list">
    <view
      class="recipe-item"
      wx:for="{{randomRecipes}}"
      wx:key="id"
      data-id="{{item.id}}"
      bindtap="onRecipeClick"
    >
      <view class="recipe-name">{{item.name}}</view>
      <view class="recipe-meta">
        <text class="difficulty">{{getDifficultyDesc(item.difficultyLevel)}}</text>
      </view>
    </view>
  </view>

  <view class="refresh-btn" bindtap="refreshRandomRecipes">
    换一批推荐
  </view>
</view>
```

### 5. 辅助方法示例

```javascript
// 在页面 JS 中添加难度等级转换方法
Page({
  data: {
    randomRecipes: []
  },

  // 将难度等级数字转换为描述文字
  getDifficultyDesc: function(level) {
    const difficultyMap = {
      1: '简单',
      2: '中等',
      3: '困难'
    };
    return difficultyMap[level] || '未知';
  },

  // 其他方法...
});
```

### 6. 获取随机菜谱并加载详情的完整示例

```javascript
// 获取随机菜谱并自动加载第一个菜谱的详情
async function getRandomRecipesWithDetail() {
  try {
    // 1. 获取随机菜谱列表
    const randomRecipes = await getRandomRecipesPromise();

    // 2. 获取第一个菜谱的详情作为推荐
    if (randomRecipes.length > 0) {
      const firstRecipe = randomRecipes[0];
      const recipeDetail = await getRecipeDetailPromise(firstRecipe.id);

      // 3. 更新页面数据
      this.setData({
        randomRecipes: randomRecipes,
        recommendedRecipe: recipeDetail
      });
    }

  } catch (error) {
    console.error('获取随机菜谱失败：', error);
    wx.showToast({
      title: '获取推荐菜谱失败',
      icon: 'none'
    });
  }
}

// 获取菜谱详情的Promise方法
function getRecipeDetailPromise(recipeId) {
  return new Promise((resolve, reject) => {
    const token = wx.getStorageSync('jwt_token');

    wx.request({
      url: `https://your-domain.com/recipe/detail/${recipeId}`,
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: function(res) {
        if (res.data.code === 200) {
          resolve(res.data.data);
        } else {
          reject(new Error(res.data.msg || '获取详情失败'));
        }
      },
      fail: function(error) {
        reject(error);
      }
    });
  });
}
```

---

## 使用场景

### 1. 首页推荐
在小程序首页展示随机菜谱，为用户提供每日推荐。

### 2. 随机发现
提供"随机发现"功能，让用户探索新的菜谱。

### 3. 灵感获取
当用户不知道做什么菜时，提供随机建议。

### 4. 内容刷新
通过下拉刷新或按钮点击，获取新的随机菜谱列表。

### 5. 菜谱预览
由于返回的是简化信息，适合作为菜谱列表的预览，用户点击后可跳转到详情页获取完整信息。

---

## 注意事项

1. **认证要求**：接口需要JWT token认证，请确保在请求头中包含有效的Authorization信息。

2. **固定数量**：接口固定返回5条菜谱，无法通过参数调整数量。

3. **随机性**：每次调用都会返回不同的随机菜谱组合。

4. **分类限制**：只返回分类为1-4的菜谱。

5. **简化数据**：返回的是简化版菜谱信息，只包含 id、name、difficultyLevel 三个字段。

6. **获取详情**：如需完整菜谱信息，需要使用返回的 id 调用菜谱详情接口 `/recipe/detail/{id}`。

7. **错误处理**：请妥善处理网络错误和业务错误，提供良好的用户体验。

8. **缓存策略**：建议实现适当的缓存机制，避免频繁请求。

9. **难度显示**：前端需要自行实现难度等级数字到文字的转换（1-简单，2-中等，3-困难）。

---

## 错误码说明

| 错误码 | 说明 | 处理建议 |
|--------|------|----------|
| 200 | 成功 | 正常处理数据 |
| 401 | 未授权 | 重新登录获取token |
| 500 | 服务器内部错误 | 提示用户稍后重试 |
| 网络错误 | 网络连接失败 | 检查网络连接 |

---

## 联系方式

如有接口相关问题，请联系开发团队：
- 开发者：jiajunwang
- 项目：铜豌豆-1forall.cn
