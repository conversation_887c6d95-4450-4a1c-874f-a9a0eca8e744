# MyBatis-Plus SQL 日志配置测试指南

## 配置更新说明

### 1. 主要配置变更

#### application.yml 更新
```yaml
# 日志配置
logging:
  config: classpath:config/logback-config.xml
  level:
    com:
      cook:
        service: debug
        model:
          mapper: debug
    # MyBatis-Plus SQL日志
    "com.baomidou.mybatisplus": debug
    # MyBatis原生SQL日志
    "org.apache.ibatis": debug
    # 数据库连接池日志
    "com.zaxxer.hikari": debug

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    # 使用SLF4J日志实现，这样SQL日志会通过logback输出到文件
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    # 驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 开启懒加载
    lazy-loading-enabled: true
    # 开启积极懒加载
    aggressive-lazy-loading: false
  # 实体类包路径
  type-aliases-package: com.cook.model
  # Mapper XML文件路径
  mapper-locations: classpath*:mapper/**/*.xml
  global-config:
    db-config:
      # 主键策略：自增
      id-type: auto
      # 逻辑删除字段
      logic-delete-field: deleted
      # 逻辑删除值
      logic-delete-value: 1
      # 逻辑未删除值
      logic-not-delete-value: 0
```

#### logback-config.xml 更新
- 新增专门的 SQL 日志文件：`/Users/<USER>/logs/cook/sql.log`
- 配置了针对性的 logger：
  - `com.cook.model.mapper` - 项目 Mapper 接口
  - `com.baomidou.mybatisplus` - MyBatis-Plus 框架
  - `org.apache.ibatis` - MyBatis 原生
  - `com.zaxxer.hikari` - 数据库连接池

### 2. 日志文件位置

SQL 日志将输出到以下位置：
- **专用 SQL 日志**：`/Users/<USER>/logs/cook/sql.log`
- **综合日志**：`/Users/<USER>/logs/cook/cook.log`
- **错误日志**：`/Users/<USER>/logs/cook/cook_error.log`
- **控制台输出**：同时在控制台显示

## 测试步骤

### 1. 启动应用
```bash
cd /Users/<USER>/Desktop/2021v2/cook
mvn spring-boot:run
```

### 2. 调用接口触发 SQL 执行
```bash
# 测试随机菜谱接口
curl -X GET "http://localhost:8082/recipe/search-by-random" \
  -H "Authorization: Bearer your_jwt_token"

# 测试菜谱搜索接口
curl -X POST "http://localhost:8082/recipe/search" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_jwt_token" \
  -d '{
    "recipeName": "炒",
    "pageNum": 1,
    "pageSize": 5
  }'

# 测试菜谱详情接口
curl -X GET "http://localhost:8082/recipe/detail/1" \
  -H "Authorization: Bearer your_jwt_token"
```

### 3. 检查日志文件
```bash
# 查看 SQL 专用日志
tail -f /Users/<USER>/logs/cook/sql.log

# 查看综合日志中的 SQL 部分
grep -i "select\|insert\|update\|delete" /Users/<USER>/logs/cook/cook.log

# 实时监控 SQL 日志
tail -f /Users/<USER>/logs/cook/sql.log | grep -E "(SELECT|INSERT|UPDATE|DELETE|Preparing|Parameters|Total)"
```

## 预期日志格式

### 成功配置后的 SQL 日志示例
```
2025-07-31 10:30:15.123 [http-nio-8082-exec-1] DEBUG [] com.cook.model.mapper.CookRecipeMapper.selectPage - ==>  Preparing: SELECT id,name,description,difficulty_level,servings,category FROM cook_recipe WHERE category IN (?,?,?,?) ORDER BY id ASC LIMIT ?
2025-07-31 10:30:15.124 [http-nio-8082-exec-1] DEBUG [] com.cook.model.mapper.CookRecipeMapper.selectPage - ==> Parameters: 1(Integer), 2(Integer), 3(Integer), 4(Integer), 5(Integer)
2025-07-31 10:30:15.135 [http-nio-8082-exec-1] DEBUG [] com.cook.model.mapper.CookRecipeMapper.selectPage - <==      Total: 5
```

### 连接池日志示例
```
2025-07-31 10:30:15.100 [http-nio-8082-exec-1] DEBUG [] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Acquired connection conn123
2025-07-31 10:30:15.140 [http-nio-8082-exec-1] DEBUG [] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Return connection conn123
```

## 故障排除

### 1. 如果没有 SQL 日志输出

**检查日志目录权限**：
```bash
# 确保日志目录存在且有写权限
mkdir -p /Users/<USER>/logs/cook
chmod 755 /Users/<USER>/logs/cook
```

**检查配置是否生效**：
```bash
# 查看应用启动日志，确认配置加载
grep -i "mybatis\|logback" /Users/<USER>/logs/cook/cook.log
```

### 2. 如果只有控制台输出没有文件输出

**检查 logback 配置**：
- 确认 `log.path` 变量设置正确
- 确认 appender 配置正确
- 检查文件权限

### 3. 如果 SQL 日志级别不对

**调整日志级别**：
```yaml
logging:
  level:
    # 如果日志太多，可以调整为 INFO
    "com.baomidou.mybatisplus": info
    # 如果需要更详细的日志，保持 DEBUG
    "org.apache.ibatis": debug
```

## 性能注意事项

### 1. 生产环境建议
- SQL 日志级别设置为 `INFO` 或 `WARN`
- 定期清理日志文件
- 考虑使用异步日志

### 2. 开发环境优化
```yaml
# 开发环境可以使用更详细的日志
logging:
  level:
    "com.baomidou.mybatisplus": trace
    "org.apache.ibatis": trace
```

## 验证清单

- [ ] 应用启动无错误
- [ ] `/Users/<USER>/logs/cook/sql.log` 文件存在
- [ ] 调用接口后能看到 SQL 语句
- [ ] 能看到 SQL 参数和执行结果
- [ ] 控制台和文件都有日志输出
- [ ] 日志格式正确，包含时间戳和线程信息

## 常用日志分析命令

```bash
# 统计今天执行的 SQL 数量
grep "$(date +%Y-%m-%d)" /Users/<USER>/logs/cook/sql.log | grep "Preparing" | wc -l

# 查找慢查询（假设超过1秒的查询）
grep -A 3 -B 1 "Total.*[1-9][0-9][0-9][0-9]" /Users/<USER>/logs/cook/sql.log

# 查看最近的 SQL 执行
tail -20 /Users/<USER>/logs/cook/sql.log | grep -E "(Preparing|Parameters|Total)"

# 按表名统计 SQL 执行次数
grep "FROM\|UPDATE\|INSERT INTO" /Users/<USER>/logs/cook/sql.log | \
  sed -E 's/.*FROM ([a-zA-Z_]+).*/\1/' | \
  sort | uniq -c | sort -nr
```
